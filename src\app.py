# from flask import Flask, request, jsonify
# from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
# from flask_cors import CORS
# import sys
# import os
# import json
# import uuid
# from datetime import datetime, timezone, timedelta
# import base64
# import asyncio
# import threading
# # LiveKit imports
# from livekit import api, rtc
# from livekit.api import AccessToken, VideoGrants, LiveKitAPI
# from livekit.rtc import Room, TrackSource
# # Add parent directory to path to import shared modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# from dotenv import load_dotenv
# from shared.database import Database # Import Database
# # Import ProxyFix for handling reverse proxy headers
# from werkzeug.middleware.proxy_fix import ProxyFix

# load_dotenv()
# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# # Middleware to keep connections alive - ADDED THIS SECTION
# @app.before_request
# def before_request():
#     # For WebSocket connections, set a longer timeout
#     if request.headers.get('Upgrade') == 'websocket':
#         socketio.server.eio.ping_timeout = 300  # 5 minutes (was 60)
#         socketio.server.eio.ping_interval = 60  # 1 minute (was 25)

# # Configure CORS to allow all origins for development
# CORS(app,
#      origins="*",
#      allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
#      supports_credentials=True,
#      methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# # Apply ProxyFix middleware to handle Nginx proxy headers
# app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# # Initialize Socket.IO with CORS support for all origins
# # async_mode will be auto-detected (eventlet in production, threading in development)
# socketio = SocketIO(app,
#                     cors_allowed_origins="*",
#                     cors_credentials=True,
#                     logger=True,
#                     engineio_logger=True,
#                     ping_timeout=300,  # 5 minutes
#                     ping_interval=60)    # 1 minute

# # Initialize Database
# db = Database()

# # Add CORS preflight handler
# @app.before_request
# def handle_preflight():
#     if request.method == "OPTIONS":
#         response = jsonify({'status': 'OK'})
#         response.headers.add("Access-Control-Allow-Origin", "*")
#         response.headers.add('Access-Control-Allow-Headers', "*")
#         response.headers.add('Access-Control-Allow-Methods', "*")
#         return response

# # LiveKit Configuration from environment variables
# LIVEKIT_URL = os.getenv('LIVEKIT_URL')
# LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
# LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# # Initialize LiveKit API Client - Lazy Initialization
# livekit_api = None

# def get_livekit_api():
#     """Get or create LiveKit API client"""
#     global livekit_api
#     if livekit_api is None:
#         livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#     return livekit_api

# # Global storage for enhanced streaming features
# enhanced_streams = {}
# stream_recordings = {}
# chat_messages = {}
# quality_settings = {}
# livekit_rooms = {}  # Track LiveKit rooms

# class EnhancedStreamManager:
#     """Manages enhanced streaming sessions"""
#     def __init__(self):
#         self.streams = {}
    
#     def create_enhanced_stream(self, teacher_id, session_id, teacher_sid, quality):
#         """Create a new enhanced streaming session"""
#         self.streams[session_id] = {
#             'teacher_id': teacher_id,
#             'session_id': session_id,
#             'teacher_sid': teacher_sid,
#             'quality': quality,
#             'viewer_sids': [],
#             'start_time': datetime.now(timezone.utc),
#             'status': 'active',
#             'viewer_count': 0,
#             'chat_enabled': True,
#             'recording_enabled': True,
#             'screen_sharing': True
#         }
#         return self.streams[session_id]
    
#     def get_stream(self, session_id):
#         """Get stream details by session ID"""
#         return self.streams.get(session_id)
    
#     def add_viewer(self, session_id, viewer_sid):
#         """Add a viewer to a stream"""
#         stream = self.get_stream(session_id)
#         if stream:
#             if viewer_sid not in stream['viewer_sids']:
#                 stream['viewer_sids'].append(viewer_sid)
#                 stream['viewer_count'] += 1
#             return True
#         return False
    
#     def remove_viewer(self, session_id, viewer_sid):
#         """Remove a viewer from a stream"""
#         stream = self.get_stream(session_id)
#         if stream and viewer_sid in stream['viewer_sids']:
#             stream['viewer_sids'].remove(viewer_sid)
#             stream['viewer_count'] = max(0, stream['viewer_count'] - 1)
#             return True
#         return False
    
#     def update_stream_status(self, session_id, status):
#         """Update stream status"""
#         stream = self.get_stream(session_id)
#         if stream:
#             stream['status'] = status
#             return True
#         return False

# class LiveKitManager:
#     """LiveKit integration manager for room and token management"""
#     def __init__(self):
#         self.rooms = {}
    
#     def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
#         """Generate LiveKit access token for a participant with extended expiration"""
#         try:
#             token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#             token.with_identity(participant_identity)
            
#             # Set token expiration to 5 hours (18000 seconds) - MODIFIED THIS SECTION
#             token.with_ttl(timedelta(seconds=18000))  # 5 hours in seconds
            
#             # Set video grants based on participant role
#             video_grants = VideoGrants()
#             video_grants.room = room_name
#             video_grants.room_join = True
            
#             if is_teacher:
#                 video_grants.can_publish = True
#                 video_grants.can_subscribe = True
#                 video_grants.can_publish_data = True
#             else:
#                 video_grants.can_publish = False
#                 video_grants.can_subscribe = True
#                 video_grants.can_publish_data = False
            
#             token.with_grants(video_grants)
            
#             if participant_name:
#                 token.with_name(participant_name)
                
#             return token.to_jwt()
#         except Exception as e:
#             print(f"❌ Error generating LiveKit token: {e}")
#             return None
    
#     async def create_room(self, room_name, max_participants=50):
#         """Create a LiveKit room"""
#         try:
#             room_request = api.CreateRoomRequest(
#                 name=room_name,
#                 max_participants=max_participants,
#                 empty_timeout=30 * 60,  # 30 minutes
#                 departure_timeout=120    # 2 minutes
#             )
#             api_client = get_livekit_api()
#             room = await api_client.room.create_room(room_request)
#             self.rooms[room_name] = {
#                 'room': room,
#                 'created_at': datetime.now(timezone.utc),
#                 'participants': [],
#                 'max_participants': max_participants
#             }
#             print(f"✅ Created LiveKit room: {room_name}")
#             return room
#         except Exception as e:
#             print(f"❌ Error creating LiveKit room {room_name}: {e}")
#             return None
    
#     async def delete_room(self, room_name):
#         """Delete a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             await api_client.room.delete_room(room_name)
#             if room_name in self.rooms:
#                 del self.rooms[room_name]
#             print(f"✅ Deleted LiveKit room: {room_name}")
#             return True
#         except Exception as e:
#             print(f"❌ Error deleting LiveKit room {room_name}: {e}")
#             return False
    
#     async def list_rooms(self):
#         """List all LiveKit rooms"""
#         try:
#             api_client = get_livekit_api()
#             rooms = await api_client.room.list_rooms()
#             return rooms
#         except Exception as e:
#             print(f"❌ Error listing LiveKit rooms: {e}")
#             return []

# # Initialize managers
# enhanced_stream_manager = EnhancedStreamManager()
# livekit_manager = LiveKitManager()

# # Health check endpoint
# @app.route('/health', methods=['GET'])
# def health_check():
#     """Enhanced streaming service health check with LiveKit status"""
#     livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
#     return jsonify({
#         'status': 'healthy',
#         'service': 'Enhanced Real-time Streaming Service with LiveKit',
#         'port': 8012,  # Updated to match the running port
#         'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
#         'active_streams': len(enhanced_stream_manager.streams),
#         'livekit': {
#             'status': livekit_status,
#             'url': LIVEKIT_URL,
#             'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
#             'rooms_managed': len(livekit_manager.rooms),
#             'token_ttl': '5 hours (18000 seconds)'  # Added token TTL info
#         },
#         'timestamp': datetime.now().isoformat()
#     }), 200

# # HTTP-based Chat API Endpoints
# @app.route('/api/chat/send', methods=['POST'])
# def send_chat_message():
#     """Send a chat message via HTTP"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')
        
#         if not session_id or not message or not sender_id:
#             return jsonify({'message': 'session_id, message, and sender_id are required'}), 400
        
#         # Store message in database
#         db.execute_query(
#             "INSERT INTO livekit_chat_messages (session_id, message, sender_id, sender_name, timestamp) "
#             "VALUES (%s, %s, %s, %s, NOW())",
#             (session_id, message, sender_id, sender_name)
#         )
        
#         # Broadcast via Socket.IO
#         socketio.emit('chat_message', {
#             'session_id': session_id,
#             'message': message,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'timestamp': datetime.now().isoformat()
#         }, room=session_id)
        
#         return jsonify({'message': 'Chat message sent successfully'}), 200
#     except Exception as e:
#         print(f"Chat message error: {e}")
#         return jsonify({'message': 'Failed to send chat message', 'error': str(e)}), 500

# @app.route('/api/chat/history', methods=['GET'])
# def get_chat_history():
#     """Get chat history for a session"""
#     try:
#         session_id = request.args.get('session_id')
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
        
#         # Fetch messages from database
#         messages = db.execute_query(
#             "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",
#             (session_id,)
#         )
        
#         return jsonify({
#             'session_id': session_id,
#             'messages': [{
#                 'id': msg['id'],
#                 'message': msg['message'],
#                 'sender_id': msg['sender_id'],
#                 'sender_name': msg['sender_name'],
#                 'timestamp': msg['timestamp'].isoformat()
#             } for msg in messages]
#         }), 200
#     except Exception as e:
#         print(f"Chat history error: {e}")
#         return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/start', methods=['POST'])
# def start_enhanced_stream():
#     """Start enhanced streaming session with LiveKit"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id', str(uuid.uuid4()))
#         quality = data.get('quality', 'medium')
#         teacher_id = data.get('teacher_id', 'demo_teacher')
#         teacher_name = data.get('teacher_name', teacher_id)
        
#         stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, None, quality)
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
        
#         if not teacher_token:
#             return jsonify({'message': 'Failed to generate LiveKit token'}), 500
        
#         # Create LiveKit room if it doesn't exist
#         if session_id not in livekit_manager.rooms:
#             loop = asyncio.new_event_loop()
#             asyncio.set_event_loop(loop)
#             loop.run_until_complete(livekit_manager.create_room(session_id))
        
#         return jsonify({
#             'session_id': session_id,
#             'livekit_token': teacher_token,
#             'livekit_url': LIVEKIT_URL,
#             'roomName': session_id,
#             'quality': quality,
#             'start_time': stream['start_time'].isoformat(),
#             'message': 'Enhanced stream started successfully'
#         }), 200
#     except Exception as e:
#         print(f"Enhanced stream start error: {e}")
#         return jsonify({'message': 'Failed to start enhanced stream'}), 500

# @app.route('/api/enhanced-stream/refresh-token', methods=['POST'])
# def refresh_livekit_token():
#     """Refresh LiveKit access token for long streaming sessions"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         room_name = data.get('room_name', session_id)
        
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
        
#         # Get the stream to verify it exists
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
        
#         # Generate a new token with extended expiration
#         user_id = data.get('user_id', f"refresh-{session_id}")
#         user_name = data.get('user_name', user_id)
#         is_teacher = data.get('is_teacher', False)
        
#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
        
#         if token:
#             return jsonify({
#                 'livekit_token': token,
#                 'room_name': room_name,
#                 'expires_in': 18000  # 5 hours in seconds
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         print(f"Token refresh error: {e}")
#         return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

# @app.route('/api/livekit/token', methods=['POST'])
# def generate_livekit_token():
#     """Generate LiveKit access token for participants"""
#     try:
#         data = request.get_json()
#         room_name = data.get('room_name') or data.get('session_id')
#         participant_id = data.get('participant_id') or data.get('user_id')
#         participant_name = data.get('participant_name', participant_id)
#         is_teacher = data.get('is_teacher', False)
        
#         if not room_name or not participant_id:
#             return jsonify({'message': 'room_name and participant_id are required'}), 400
        
#         stream = enhanced_stream_manager.get_stream(room_name)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
        
#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=participant_id,
#             participant_name=participant_name,
#             is_teacher=is_teacher
#         )
        
#         if token:
#             return jsonify({
#                 'token': token,
#                 'livekit_url': LIVEKIT_URL,
#                 'room_name': room_name,
#                 'participant_id': participant_id,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000  # 5 hours in seconds
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         print(f"Token generation error: {e}")
#         return jsonify({'message': 'Failed to generate token'}), 500

# @app.route('/api/livekit/join', methods=['POST'])
# def join_livekit_room():
#     """Join LiveKit room and get connection details"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')
        
#         if not session_id or not user_id:
#             return jsonify({'message': 'session_id and user_id are required'}), 400
        
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
        
#         # Add user to stream viewers
#         if user_role != 'teacher':
#             enhanced_stream_manager.add_viewer(session_id, f"viewer-{user_id}")
        
#         # Generate token
#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
        
#         if token:
#             return jsonify({
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000  # 5 hours in seconds
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate LiveKit token'}), 500
#     except Exception as e:
#         print(f"Error joining LiveKit room: {e}")
#         return jsonify({'message': 'Failed to join LiveKit room', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/stop', methods=['POST'])
# def stop_enhanced_stream():
#     """Stop enhanced streaming session"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
        
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
        
#         # Remove from stream manager
#         if session_id in enhanced_stream_manager.streams:
#             del enhanced_stream_manager.streams[session_id]
        
#         # Delete LiveKit room
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         loop.run_until_complete(livekit_manager.delete_room(session_id))
        
#         return jsonify({'message': 'Stream stopped successfully'}), 200
#     except Exception as e:
#         print(f"Stream stop error: {e}")
#         return jsonify({'message': 'Failed to stop stream', 'error': str(e)}), 500

# @app.route('/active-streams', methods=['GET'])
# def get_active_streams():
#     """Get all active enhanced streams"""
#     try:
#         active_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             if stream['status'] == 'active':
#                 stream_info = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'created_at': stream['start_time'].isoformat(),
#                     'uptime': (datetime.now(timezone.utc) - stream['start_time']).total_seconds(),
#                     'features': {
#                         'chat_enabled': stream.get('chat_enabled', True),
#                         'recording_enabled': stream.get('recording_enabled', True),
#                         'screen_sharing': stream.get('screen_sharing', True)
#                     }
#                 }
#                 active_streams.append(stream_info)
#         return jsonify({
#             'success': True,
#             'streams': active_streams,
#             'active_streams': active_streams,
#             'total_count': len(active_streams),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error getting active streams: {e}")
#         return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500


# @app.route('/api/enhanced-stream/list', methods=['GET'])
# def list_enhanced_streams():
#     """List all active enhanced streams"""
#     try:
#         streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             streams.append({
#                 'session_id': session_id,
#                 'teacher_id': stream['teacher_id'],
#                 'quality': stream['quality'],
#                 'status': stream['status'],
#                 'viewer_count': stream['viewer_count'],
#                 'start_time': stream['start_time'].isoformat(),
#                 'room_exists': session_id in livekit_manager.rooms
#             })
        
#         return jsonify({
#             'streams': streams,
#             'total': len(streams),
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error listing streams: {e}")
#         return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# # Socket.IO Events
# @socketio.on('connect')
# def handle_connect():
#     print(f"✅ Enhanced client connected: {request.sid}")
#     emit('connected', {'message': 'Connected to enhanced streaming service'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     print(f"❌ Enhanced client disconnected: {request.sid}")
#     for session_id in list(enhanced_stream_manager.streams.keys()):
#         if enhanced_stream_manager.remove_viewer(session_id, request.sid):
#             stream = enhanced_stream_manager.get_stream(session_id)
#             emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)

# @socketio.on('join_enhanced_stream')
# def handle_join_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return
        
#         # Join the room
#         join_room(session_id)
        
#         # Add viewer to stream
#         enhanced_stream_manager.add_viewer(session_id, request.sid)
#         stream = enhanced_stream_manager.get_stream(session_id)
        
#         if stream:
#             emit('viewer_count', {'viewer_count': stream['viewer_count']})
#             emit('stream_joined', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'status': stream['status']
#             })
            
#             # Broadcast to other participants
#             emit('viewer_joined', {
#                 'viewer_count': stream['viewer_count'],
#                 'viewer_id': request.sid
#             }, room=session_id, include_self=False)
#         else:
#             emit('error', {'message': 'Stream not found'})
#     except Exception as e:
#         print(f"❌ Error joining enhanced stream: {e}")
#         emit('error', {'message': 'Failed to join stream'})

# @socketio.on('leave_enhanced_stream')
# def handle_leave_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)
#     except Exception as e:
#         print(f"❌ Error leaving enhanced stream: {e}")

# @socketio.on('request_livekit_token')
# def handle_request_livekit_token(data):
#     try:
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')
        
#         if not session_id or not user_id:
#             emit('error', {'message': 'Session ID and User ID required'})
#             return
        
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return
        
#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
        
#         if token:
#             emit('livekit_token_generated', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000  # 5 hours in seconds
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         print(f"❌ Error generating LiveKit token: {e}")
#         emit('error', {'message': 'Failed to generate token'})

# @socketio.on('start_stream')
# def handle_start_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         teacher_name = data.get('teacher_name', teacher_id)
#         quality = data.get('quality', 'medium')
        
#         if not session_id or not teacher_id:
#             emit('error', {'message': 'Session ID and Teacher ID required'})
#             return
        
#         stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, request.sid, quality)
#         join_room(session_id)
        
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
        
#         if teacher_token:
#             emit('stream_started', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': teacher_token,
#                 'quality': quality,
#                 'token_expires_in': 18000  # 5 hours in seconds
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         print(f"❌ Error starting stream: {e}")
#         emit('error', {'message': 'Failed to start stream'})

# @socketio.on('stop_stream')
# def handle_stop_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return
        
#         # Remove from stream manager
#         if session_id in enhanced_stream_manager.streams:
#             del enhanced_stream_manager.streams[session_id]
        
#         # Leave room
#         leave_room(session_id)
        
#         # Delete LiveKit room
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         loop.run_until_complete(livekit_manager.delete_room(session_id))
        
#         emit('stream_stopped', {'session_id': session_id})
#     except Exception as e:
#         print(f"❌ Error stopping stream: {e}")
#         emit('error', {'message': 'Failed to stop stream'})

# @socketio.on('enhanced_video_frame')
# def handle_enhanced_video_frame(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame')
        
#         if session_id and frame_data:
#             # Broadcast frame to all viewers in the room except the sender
#             emit('video_frame', {'frame': frame_data}, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"Video frame error: {e}")

# # Start the server
# if __name__ == '__main__':
#     print("=" * 70)
#     print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
#     print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
#     print("🚀 Server starting on port 8012...")
#     print("=" * 70)
    
#     if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
#         print("❌ LiveKit configuration missing! Please check your .env file.")
#         print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
#         exit(1)
    
#     print("✅ LiveKit configuration validated")
#     print("🎬 Ready to create streaming rooms with LiveKit integration")
    
#     DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']
    
#     # For production deployment, use allow_unsafe_werkzeug=True
#     # Note: For better production performance, consider using Gunicorn
#     socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)

from flask import Flask, request, jsonify
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import sys
import os
import json
import uuid
from datetime import datetime, timezone, timedelta
import base64
import asyncio
import threading
# LiveKit imports
from livekit import api, rtc
from livekit.api import AccessToken, VideoGrants, LiveKitAPI
from livekit.rtc import Room, TrackSource
# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dotenv import load_dotenv
from shared.database import Database # Import Database
# Import ProxyFix for handling reverse proxy headers
from werkzeug.middleware.proxy_fix import ProxyFix

load_dotenv()
app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# Middleware to keep connections alive - ENHANCED VERSION
@app.before_request
def before_request():
    # For WebSocket connections, set extended timeout values
    if request.headers.get('Upgrade') == 'websocket':
        # Increase timeout values for WebSocket connections
        socketio.server.eio.ping_timeout = 600  # 10 minutes (was 60)
        socketio.server.eio.ping_interval = 120  # 2 minutes (was 25)
    
    # For regular HTTP requests, ensure proper keep-alive headers
    if not request.is_websocket:
        # Set long keep-alive to prevent premature connection closure
        response = app.make_default_options_response()
        response.headers['Connection'] = 'keep-alive'
        response.headers['Keep-Alive'] = 'timeout=600, max=1000'
        return response

# Configure CORS to allow all origins for development
CORS(app,
     origins="*",
     allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials", "Upgrade", "Connection"],
     supports_credentials=True,
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"])

# Apply ProxyFix middleware to handle Nginx proxy headers
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# Initialize Socket.IO with extended timeouts for production
# async_mode will be auto-detected (eventlet in production, threading in development)
socketio = SocketIO(app,
                    cors_allowed_origins="*",
                    cors_credentials=True,
                    logger=True,
                    engineio_logger=True,
                    ping_timeout=600,    # 10 minutes (was 60)
                    ping_interval=120,   # 2 minutes (was 25)
                    async_mode='eventlet' if os.getenv('FLASK_ENV') == 'production' else None)

# Initialize Database
db = Database()

# Add CORS preflight handler
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({'status': 'OK'})
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization,Access-Control-Allow-Credentials,Upgrade,Connection")
        response.headers.add('Access-Control-Allow-Methods', "GET,POST,PUT,DELETE,OPTIONS,HEAD")
        response.headers.add('Access-Control-Max-Age', '86400')  # Cache preflight for 24 hours
        return response

# LiveKit Configuration from environment variables
LIVEKIT_URL = os.getenv('LIVEKIT_URL')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# Initialize LiveKit API Client - Lazy Initialization
livekit_api = None

def get_livekit_api():
    """Get or create LiveKit API client"""
    global livekit_api
    if livekit_api is None:
        livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    return livekit_api

# Global storage for enhanced streaming features
enhanced_streams = {}
stream_recordings = {}
chat_messages = {}
quality_settings = {}
livekit_rooms = {}  # Track LiveKit rooms

class EnhancedStreamManager:
    """Manages enhanced streaming sessions"""
    def __init__(self):
        self.streams = {}
    
    def create_enhanced_stream(self, teacher_id, session_id, teacher_sid, quality, chat_enabled=True, recording_enabled=False, screen_sharing=True):
        """Create a new enhanced streaming session with all features"""
        self.streams[session_id] = {
            'teacher_id': teacher_id,
            'session_id': session_id,
            'teacher_sid': teacher_sid,
            'quality': quality,
            'viewer_sids': [],
            'start_time': datetime.now(timezone.utc),
            'status': 'active',
            'viewer_count': 0,
            'chat_enabled': chat_enabled,
            'recording_enabled': recording_enabled,
            'screen_sharing': screen_sharing,
            'last_activity': datetime.now(timezone.utc),
            'created_at': datetime.now(timezone.utc)
        }
        return self.streams[session_id]
    
    def get_stream(self, session_id):
        """Get stream details by session ID"""
        return self.streams.get(session_id)
    
    def add_viewer(self, session_id, viewer_sid):
        """Add a viewer to a stream"""
        stream = self.get_stream(session_id)
        if stream:
            if viewer_sid not in stream['viewer_sids']:
                stream['viewer_sids'].append(viewer_sid)
                stream['viewer_count'] += 1
                stream['last_activity'] = datetime.now(timezone.utc)
            return True
        return False
    
    def remove_viewer(self, session_id, viewer_sid):
        """Remove a viewer from a stream"""
        stream = self.get_stream(session_id)
        if stream and viewer_sid in stream['viewer_sids']:
            stream['viewer_sids'].remove(viewer_sid)
            stream['viewer_count'] = max(0, stream['viewer_count'] - 1)
            stream['last_activity'] = datetime.now(timezone.utc)
            return True
        return False
    
    def update_stream_status(self, session_id, status):
        """Update stream status"""
        stream = self.get_stream(session_id)
        if stream:
            stream['status'] = status
            stream['last_activity'] = datetime.now(timezone.utc)
            return True
        return False
    
    def stop_stream(self, session_id):
        """Stop a streaming session"""
        stream = self.get_stream(session_id)
        if stream:
            stream['status'] = 'stopped'
            stream['ended_at'] = datetime.now(timezone.utc)
            stream['last_activity'] = datetime.now(timezone.utc)
            return True
        return False

class LiveKitManager:
    """LiveKit integration manager for room and token management"""
    def __init__(self):
        self.rooms = {}
    
    def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
        """Generate LiveKit access token for a participant with extended expiration"""
        try:
            token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
            token.with_identity(participant_identity)
            
            # Set token expiration to 8 hours (28800 seconds) - EXTENDED FOR VPS DEPLOYMENT
            token.with_ttl(28800)  # 8 hours in seconds
            
            # Set video grants based on participant role
            video_grants = VideoGrants()
            video_grants.room = room_name
            video_grants.room_join = True
            
            if is_teacher:
                video_grants.can_publish = True
                video_grants.can_subscribe = True
                video_grants.can_publish_data = True
            else:
                video_grants.can_publish = False
                video_grants.can_subscribe = True
                video_grants.can_publish_data = False
            
            token.add_grant(video_grants)
            
            if participant_name:
                token.with_name(participant_name)
                
            return token.to_jwt()
        except Exception as e:
            print(f"❌ Error generating LiveKit token: {e}")
            return None
    
    async def create_room(self, room_name, max_participants=50):
        """Create a LiveKit room with extended timeouts"""
        try:
            room_request = api.CreateRoomRequest(
                name=room_name,
                max_participants=max_participants,
                empty_timeout=60 * 60,  # 60 minutes (was 30)
                departure_timeout=300    # 5 minutes (was 120)
            )
            api_client = get_livekit_api()
            room = await api_client.room.create_room(room_request)
            self.rooms[room_name] = {
                'room': room,
                'created_at': datetime.now(timezone.utc),
                'participants': [],
                'max_participants': max_participants,
                'last_activity': datetime.now(timezone.utc)
            }
            print(f"✅ Created LiveKit room: {room_name}")
            return room
        except Exception as e:
            print(f"❌ Error creating LiveKit room {room_name}: {e}")
            return None
    
    async def delete_room(self, room_name):
        """Delete a LiveKit room"""
        try:
            api_client = get_livekit_api()
            await api_client.room.delete_room(room_name)
            if room_name in self.rooms:
                del self.rooms[room_name]
            print(f"✅ Deleted LiveKit room: {room_name}")
            return True
        except Exception as e:
            print(f"❌ Error deleting LiveKit room {room_name}: {e}")
            return False
    
    async def list_rooms(self):
        """List all LiveKit rooms"""
        try:
            api_client = get_livekit_api()
            rooms = await api_client.room.list_rooms()
            return rooms
        except Exception as e:
            print(f"❌ Error listing LiveKit rooms: {e}")
            return []

# Initialize managers
enhanced_stream_manager = EnhancedStreamManager()
livekit_manager = LiveKitManager()

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced streaming service health check with LiveKit status"""
    livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
    return jsonify({
        'status': 'healthy',
        'service': 'Enhanced Real-time Streaming Service with LiveKit',
        'port': 8012,  # Updated to match the running port
        'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
        'active_streams': len(enhanced_stream_manager.streams),
        'livekit': {
            'status': livekit_status,
            'url': LIVEKIT_URL,
            'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
            'rooms_managed': len(livekit_manager.rooms),
            'token_ttl': '8 hours (28800 seconds)'  # Updated token TTL info
        },
        'socketio_config': {
            'ping_timeout': 600,  # 10 minutes
            'ping_interval': 120, # 2 minutes
            'async_mode': socketio.async_mode
        },
        'timestamp': datetime.now().isoformat()
    }), 200

# HTTP-based Chat API Endpoints
@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """Send a chat message via HTTP"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')
        
        if not session_id or not message or not sender_id:
            return jsonify({'message': 'session_id, message, and sender_id are required'}), 400
        
        # Store message in database
        db.execute_query(
            "INSERT INTO livekit_chat_messages (session_id, message, sender_id, sender_name, timestamp) "
            "VALUES (%s, %s, %s, %s, NOW())",
            (session_id, message, sender_id, sender_name)
        )
        
        # Update stream last activity
        stream = enhanced_stream_manager.get_stream(session_id)
        if stream:
            stream['last_activity'] = datetime.now(timezone.utc)
        
        # Broadcast via Socket.IO
        socketio.emit('chat_message', {
            'session_id': session_id,
            'message': message,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'timestamp': datetime.now().isoformat()
        }, room=session_id)
        
        return jsonify({'message': 'Chat message sent successfully'}), 200
    except Exception as e:
        print(f"Chat message error: {e}")
        return jsonify({'message': 'Failed to send chat message', 'error': str(e)}), 500

@app.route('/api/chat/history', methods=['GET'])
def get_chat_history():
    """Get chat history for a session"""
    try:
        session_id = request.args.get('session_id')
        if not session_id:
            return jsonify({'message': 'session_id is required'}), 400
        
        # Fetch messages from database
        messages = db.execute_query(
            "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",
            (session_id,)
        )
        
        # Update stream last activity
        stream = enhanced_stream_manager.get_stream(session_id)
        if stream:
            stream['last_activity'] = datetime.now(timezone.utc)
        
        return jsonify({
            'session_id': session_id,
            'messages': [{
                'id': msg['id'],
                'message': msg['message'],
                'sender_id': msg['sender_id'],
                'sender_name': msg['sender_name'],
                'timestamp': msg['timestamp'].isoformat()
            } for msg in messages]
        }), 200
    except Exception as e:
        print(f"Chat history error: {e}")
        return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/start', methods=['POST'])
def start_enhanced_stream():
    """Start enhanced streaming session with LiveKit"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', str(uuid.uuid4()))
        quality = data.get('quality', 'medium')
        teacher_id = data.get('teacher_id', 'demo_teacher')
        teacher_name = data.get('teacher_name', teacher_id)
        chat_enabled = data.get('chat_enabled', True)
        recording_enabled = data.get('recording_enabled', False)
        screen_sharing = data.get('screen_sharing', True)
        
        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, None, quality, chat_enabled, recording_enabled, screen_sharing
        )
        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )
        
        if not teacher_token:
            return jsonify({'message': 'Failed to generate LiveKit token'}), 500
        
        # Create LiveKit room if it doesn't exist
        if session_id not in livekit_manager.rooms:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(livekit_manager.create_room(session_id))
        
        return jsonify({
            'session_id': session_id,
            'livekit_token': teacher_token,
            'livekit_url': LIVEKIT_URL,
            'roomName': session_id,
            'quality': quality,
            'start_time': stream['start_time'].isoformat(),
            'message': 'Enhanced stream started successfully',
            'features': {
                'chat_enabled': chat_enabled,
                'recording_enabled': recording_enabled,
                'screen_sharing': screen_sharing
            },
            'token_expires_in': 28800  # 8 hours in seconds
        }), 200
    except Exception as e:
        print(f"Enhanced stream start error: {e}")
        return jsonify({'message': 'Failed to start enhanced stream'}), 500

@app.route('/api/enhanced-stream/refresh-token', methods=['POST'])
def refresh_livekit_token():
    """Refresh LiveKit access token for long streaming sessions"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        room_name = data.get('room_name', session_id)
        
        if not session_id:
            return jsonify({'message': 'session_id is required'}), 400
        
        # Get the stream to verify it exists
        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404
        
        # Generate a new token with extended expiration
        user_id = data.get('user_id', f"refresh-{session_id}")
        user_name = data.get('user_name', user_id)
        is_teacher = data.get('is_teacher', False)
        
        token = livekit_manager.generate_access_token(
            room_name=room_name,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )
        
        if token:
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
            
            return jsonify({
                'livekit_token': token,
                'room_name': room_name,
                'expires_in': 28800,  # 8 hours in seconds
                'message': 'Token refreshed successfully'
            }), 200
        else:
            return jsonify({'message': 'Failed to generate token'}), 500
    except Exception as e:
        print(f"Token refresh error: {e}")
        return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

@app.route('/api/livekit/token', methods=['POST'])
def generate_livekit_token():
    """Generate LiveKit access token for participants"""
    try:
        data = request.get_json()
        room_name = data.get('room_name') or data.get('session_id')
        participant_id = data.get('participant_id') or data.get('user_id')
        participant_name = data.get('participant_name', participant_id)
        is_teacher = data.get('is_teacher', False)
        
        if not room_name or not participant_id:
            return jsonify({'message': 'room_name and participant_id are required'}), 400
        
        stream = enhanced_stream_manager.get_stream(room_name)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404
        
        token = livekit_manager.generate_access_token(
            room_name=room_name,
            participant_identity=participant_id,
            participant_name=participant_name,
            is_teacher=is_teacher
        )
        
        if token:
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
            
            return jsonify({
                'token': token,
                'livekit_url': LIVEKIT_URL,
                'room_name': room_name,
                'participant_id': participant_id,
                'is_teacher': is_teacher,
                'token_expires_in': 28800  # 8 hours in seconds
            }), 200
        else:
            return jsonify({'message': 'Failed to generate token'}), 500
    except Exception as e:
        print(f"Token generation error: {e}")
        return jsonify({'message': 'Failed to generate token'}), 500

@app.route('/api/livekit/join', methods=['POST'])
def join_livekit_room():
    """Join LiveKit room and get connection details"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')
        
        if not session_id or not user_id:
            return jsonify({'message': 'session_id and user_id are required'}), 400
        
        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404
        
        # Add user to stream viewers
        if user_role != 'teacher':
            enhanced_stream_manager.add_viewer(session_id, f"viewer-{user_id}")
        
        # Generate token
        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )
        
        if token:
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
            
            return jsonify({
                'session_id': session_id,
                'livekit_url': LIVEKIT_URL,
                'livekit_token': token,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher,
                'token_expires_in': 28800  # 8 hours in seconds
            }), 200
        else:
            return jsonify({'message': 'Failed to generate LiveKit token'}), 500
    except Exception as e:
        print(f"Error joining LiveKit room: {e}")
        return jsonify({'message': 'Failed to join LiveKit room', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/stop', methods=['POST'])
def stop_enhanced_stream():
    """Stop enhanced streaming session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        
        if not session_id:
            return jsonify({'message': 'session_id is required'}), 400
        
        # Remove from stream manager
        success = enhanced_stream_manager.stop_stream(session_id)
        if not success:
            return jsonify({'message': 'Stream not found'}), 404
        
        # Delete LiveKit room
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(livekit_manager.delete_room(session_id))
        
        # Broadcast stream ended event
        socketio.emit('stream_ended', {
            'session_id': session_id,
            'message': 'Stream has ended'
        }, room=session_id)
        
        return jsonify({'message': 'Stream stopped successfully'}), 200
    except Exception as e:
        print(f"Stream stop error: {e}")
        return jsonify({'message': 'Failed to stop stream', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/list', methods=['GET'])
def list_enhanced_streams():
    """List all active enhanced streams"""
    try:
        streams = []
        for session_id, stream in enhanced_stream_manager.streams.items():
            streams.append({
                'session_id': session_id,
                'teacher_id': stream['teacher_id'],
                'quality': stream['quality'],
                'status': stream['status'],
                'viewer_count': stream['viewer_count'],
                'start_time': stream['start_time'].isoformat(),
                'last_activity': stream['last_activity'].isoformat(),
                'room_exists': session_id in livekit_manager.rooms,
                'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
                'features': {
                    'chat_enabled': stream['chat_enabled'],
                    'recording_enabled': stream['recording_enabled'],
                    'screen_sharing': stream['screen_sharing']
                }
            })
        
        return jsonify({
            'streams': streams,
            'total': len(streams),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 200
    except Exception as e:
        print(f"❌ Error listing streams: {e}")
        return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# New endpoint to get stream status with activity check
@app.route('/api/enhanced-stream/status/<session_id>', methods=['GET'])
def get_stream_status(session_id):
    """Get detailed stream status with activity monitoring"""
    try:
        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404
        
        # Calculate uptime and check if stream is active
        now = datetime.now(timezone.utc)
        uptime = (now - stream['created_at']).total_seconds()
        
        # Check if stream should be considered active (last activity within 20 minutes)
        is_active = (now - stream['last_activity']).total_seconds() < 1200  # 20 minutes
        
        return jsonify({
            'session_id': session_id,
            'status': stream['status'],
            'viewer_count': stream['viewer_count'],
            'quality': stream['quality'],
            'start_time': stream['start_time'].isoformat(),
            'last_activity': stream['last_activity'].isoformat(),
            'uptime': uptime,
            'is_active': is_active,
            'features': {
                'chat_enabled': stream['chat_enabled'],
                'recording_enabled': stream['recording_enabled'],
                'screen_sharing': stream['screen_sharing']
            }
        }), 200
    except Exception as e:
        print(f"Error getting stream status: {e}")
        return jsonify({'message': 'Failed to get stream status', 'error': str(e)}), 500

# Socket.IO Events
@socketio.on('connect')
def handle_connect():
    print(f"✅ Enhanced client connected: {request.sid}")
    emit('connected', {'message': 'Connected to enhanced streaming service'})

@socketio.on('disconnect')
def handle_disconnect():
    print(f"❌ Enhanced client disconnected: {request.sid}")
    for session_id in list(enhanced_stream_manager.streams.keys()):
        if enhanced_stream_manager.remove_viewer(session_id, request.sid):
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                # Emit viewer left event
                emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)
                # Update stream last activity
                stream['last_activity'] = datetime.now(timezone.utc)

@socketio.on('join_enhanced_stream')
def handle_join_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return
        
        # Join the room
        join_room(session_id)
        
        # Add viewer to stream
        enhanced_stream_manager.add_viewer(session_id, request.sid)
        stream = enhanced_stream_manager.get_stream(session_id)
        
        if stream:
            emit('viewer_count', {'viewer_count': stream['viewer_count']})
            emit('stream_joined', {
                'session_id': session_id,
                'viewer_count': stream['viewer_count'],
                'status': stream['status'],
                'features': {
                    'chat_enabled': stream['chat_enabled'],
                    'recording_enabled': stream['recording_enabled'],
                    'screen_sharing': stream['screen_sharing']
                }
            })
            
            # Broadcast to other participants
            emit('viewer_joined', {
                'viewer_count': stream['viewer_count'],
                'viewer_id': request.sid
            }, room=session_id, include_self=False)
            
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
        else:
            emit('error', {'message': 'Stream not found'})
    except Exception as e:
        print(f"❌ Error joining enhanced stream: {e}")
        emit('error', {'message': 'Failed to join stream'})

@socketio.on('leave_enhanced_stream')
def handle_leave_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        if session_id:
            leave_room(session_id)
            enhanced_stream_manager.remove_viewer(session_id, request.sid)
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)
                # Update stream last activity
                stream['last_activity'] = datetime.now(timezone.utc)
    except Exception as e:
        print(f"❌ Error leaving enhanced stream: {e}")

@socketio.on('request_livekit_token')
def handle_request_livekit_token(data):
    try:
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')
        
        if not session_id or not user_id:
            emit('error', {'message': 'Session ID and User ID required'})
            return
        
        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            emit('error', {'message': 'Stream not found'})
            return
        
        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )
        
        if token:
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
            
            emit('livekit_token_generated', {
                'session_id': session_id,
                'livekit_url': LIVEKIT_URL,
                'livekit_token': token,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher,
                'token_expires_in': 28800  # 8 hours in seconds
            })
        else:
            emit('error', {'message': 'Failed to generate LiveKit token'})
    except Exception as e:
        print(f"❌ Error generating LiveKit token: {e}")
        emit('error', {'message': 'Failed to generate token'})

@socketio.on('start_stream')
def handle_start_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        teacher_name = data.get('teacher_name', teacher_id)
        quality = data.get('quality', 'medium')
        chat_enabled = data.get('chat_enabled', True)
        recording_enabled = data.get('recording_enabled', False)
        screen_sharing = data.get('screen_sharing', True)
        
        if not session_id or not teacher_id:
            emit('error', {'message': 'Session ID and Teacher ID required'})
            return
        
        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, request.sid, quality, chat_enabled, recording_enabled, screen_sharing
        )
        join_room(session_id)
        
        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )
        
        if teacher_token:
            # Update stream last activity
            stream['last_activity'] = datetime.now(timezone.utc)
            
            emit('stream_started', {
                'session_id': session_id,
                'livekit_url': LIVEKIT_URL,
                'livekit_token': teacher_token,
                'quality': quality,
                'token_expires_in': 28800,  # 8 hours in seconds
                'features': {
                    'chat_enabled': chat_enabled,
                    'recording_enabled': recording_enabled,
                    'screen_sharing': screen_sharing
                }
            })
        else:
            emit('error', {'message': 'Failed to generate LiveKit token'})
    except Exception as e:
        print(f"❌ Error starting stream: {e}")
        emit('error', {'message': 'Failed to start stream'})

@socketio.on('stop_stream')
def handle_stop_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        
        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return
        
        # Remove from stream manager
        success = enhanced_stream_manager.stop_stream(session_id)
        if not success:
            emit('error', {'message': 'Stream not found'})
            return
        
        # Leave room
        leave_room(session_id)
        
        # Delete LiveKit room
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(livekit_manager.delete_room(session_id))
        
        # Broadcast stream ended event
        emit('stream_stopped', {
            'session_id': session_id,
            'message': 'Stream stopped successfully'
        })
        
        # Notify all viewers that stream has ended
        socketio.emit('stream_ended', {
            'session_id': session_id,
            'message': 'Stream has ended'
        }, room=session_id)
        
        print(f"✅ Teacher {teacher_id} stopped stream {session_id}")
    except Exception as e:
        print(f"❌ Error stopping stream: {e}")
        emit('error', {'message': 'Failed to stop stream'})

@socketio.on('end_stream')
def handle_end_stream(data):
    try:
        print(f"🔄 Received end_stream event for session {data.get('session_id')}, teacher {data.get('teacher_id')}")
        print(f"🔄 Forwarding to stop_stream handler for backward compatibility")
        handle_stop_stream(data)
    except Exception as e:
        print(f"❌ Error handling end_stream: {e}")
        emit('error', {'message': f'Failed to end stream: {str(e)}'})

@socketio.on('chat_message')
def handle_chat_message(data):
    try:
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')
        
        if not session_id or not message or not sender_id:
            emit('error', {'message': 'session_id, message, and sender_id are required'})
            return
        
        # Store message in database
        db.execute_query(
            "INSERT INTO livekit_chat_messages (session_id, message, sender_id, sender_name, timestamp) "
            "VALUES (%s, %s, %s, %s, NOW())",
            (session_id, message, sender_id, sender_name)
        )
        
        # Update stream last activity
        stream = enhanced_stream_manager.get_stream(session_id)
        if stream:
            stream['last_activity'] = datetime.now(timezone.utc)
        
        # Broadcast chat message
        emit('chat_message', {
            'session_id': session_id,
            'message': message,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'timestamp': datetime.now().isoformat()
        }, room=session_id)
        
    except Exception as e:
        print(f"❌ Error handling chat message: {e}")
        emit('error', {'message': 'Failed to send chat message'})

@socketio.on('heartbeat')
def handle_heartbeat(data):
    """Handle heartbeat messages to keep connection alive"""
    try:
        session_id = data.get('session_id')
        if session_id:
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                # Update last activity to prevent timeout
                stream['last_activity'] = datetime.now(timezone.utc)
                # Respond with current status
                emit('heartbeat_response', {
                    'session_id': session_id,
                    'status': 'active',
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'viewer_count': stream['viewer_count']
                })
    except Exception as e:
        print(f"❌ Error handling heartbeat: {e}")

# Start the server
if __name__ == '__main__':
    print("=" * 70)
    print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
    print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
    print("🚀 Server starting on port 8012...")
    print("=" * 70)
    
    if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
        print("❌ LiveKit configuration missing! Please check your .env file.")
        print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
        exit(1)
    
    print("✅ LiveKit configuration validated")
    print("🎬 Ready to create streaming rooms with LiveKit integration")
    
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']
    
    # For production deployment, use allow_unsafe_werkzeug=True
    # Note: For better production performance, consider using Gunicorn
    socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)